﻿namespace Miller.WMS.Domain;

public enum SpecificationType
{
    Weld = 0,
    Material = 1,
    Consumable = 2,
    Qualification = 3,
    Quality = 4,
    Other = 5
}

public enum SpecificationStatus
{
    Active = 0,
    Inactive = 1,
    Superseded = 2
}

public class Specification
{
    public int Id { get; set; }

    public SpecificationType Type { get; set; }
    public string? OtherSpecification { get; set; } // required if Type == Other (enforce in domain)
    public string Code { get; set; } = null!;
    public string Title { get; set; } = null!;
    public string? Description { get; set; }

    public int IssuingOrganizationId { get; set; }
    public IssuingOrganization IssuingOrganization { get; set; } = null!;

    public SpecificationStatus Status { get; set; }

    public int? SupersededSpecId { get; set; }
    public Specification? SupersededSpec { get; set; }
    public ICollection<Specification> SupersedingSpecs { get; set; } = [];
}
