﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Miller.WMS.Domain;

public class Customer : IEntity
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;
    public string Code { get; set; } = null!;

    // Navigation property
    public ICollection<CustomerFacility> Facilities { get; set; } = [];
}