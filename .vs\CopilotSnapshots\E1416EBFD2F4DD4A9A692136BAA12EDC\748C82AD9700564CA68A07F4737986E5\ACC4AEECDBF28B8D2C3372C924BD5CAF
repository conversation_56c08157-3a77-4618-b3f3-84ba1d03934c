﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Miller.WMS.Domain;

public class CatElectrodeSpecialRule
{
    public int Id { get; set; }

    public string WeldingArcDesignationSpec { get; set; } = null!;

    // FKs
    public int WeldingProcessId { get; set; }
    public int? SpecificationId { get; set; }

    // Other fields
    public decimal? MinimumYieldStrength { get; set; }

    // Navigation properties (assumed targets; adjust names/types if your model differs)
    public WeldingProcess WeldingProcess { get; set; }
    public Specification? Specification { get; set; }
}