﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class FluxChemicalCompositionLimitsConfiguration : IEntityTypeConfiguration<FluxChemicalCompositionLimits>
{
    public void Configure(EntityTypeBuilder<FluxChemicalCompositionLimits> builder)
    {
        builder.<PERSON><PERSON>ey(e => e.Id);

        builder.Property(e => e.ChemicalConstituents)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.ConstituentLimitMin)
               .HasPrecision(5, 4)
               .IsRequired();

        builder.Property(e => e.ConstituentLimitMax)
               .HasPrecision(5, 4)
               .IsRequired();

        builder.HasOne(e => e.FluxChemicalComposition)
               .WithMany(e => e.Limits)
               .HasForeignKey(e => e.FluxChemicalCompositionId)
               .OnDelete(DeleteBehavior.Cascade);
    }
}
