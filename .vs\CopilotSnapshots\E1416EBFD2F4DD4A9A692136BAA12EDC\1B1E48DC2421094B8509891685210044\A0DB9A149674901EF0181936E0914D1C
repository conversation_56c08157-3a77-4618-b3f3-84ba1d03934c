﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Miller.WMS.Domain;

public enum ElectrodeCoveringType
{
    RutileFlux = 0,
    CellulosicFlux = 1,
    BasicFlux = 2,
    AcidFlux = 3
}

public class ElectrodeClassification
{
    public int Id { get; set; }

    public int SpecificationId { get; set; }
    public Specification Specification { get; set; } = null!;

    public string Classification { get; set; } = null!;

    public decimal YieldStrength { get; set; }      // (6,3)
    public decimal TensileStrength { get; set; }    // (6,3)
    public decimal Elongation { get; set; }         // (5,4)

    public ElectrodeCoveringType? CoveringType { get; set; }

    public int? TungstenElectrodeClassificationId { get; set; }
    public TungstenElectrodeClassification? TungstenElectrodeClassification { get; set; }

    public int MetalChemicalCompositionId { get; set; }
    public MetalChemicalComposition MetalChemicalComposition { get; set; } = null!;
}
