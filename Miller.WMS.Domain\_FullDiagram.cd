﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="Miller.WMS.Domain.CatElectrodeSpecialRule">
    <Position X="12.5" Y="17.25" Width="2.5" />
    <AssociationLine Name="Specification" Type="Miller.WMS.Domain.Specification" FixedFromPoint="true">
      <Path>
        <Point X="14.562" Y="17.25" />
        <Point X="14.562" Y="14.95" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAAABAAAgBAAAACAAAAAAAASAAAAAAAAAA=</HashCode>
      <FileName>CatElectrodeSpecialRule.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="WeldingProcess" />
      <Property Name="Specification" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Customer">
    <Position X="55" Y="18.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAAAAAAAAAgAAQAAAAAQAAAAAAAAAAAAAA=</HashCode>
      <FileName>Customer.cs</FileName>
    </TypeIdentifier>
    <ShowAsCollectionAssociation>
      <Property Name="Facilities" />
    </ShowAsCollectionAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.CustomerFacility">
    <Position X="55" Y="20.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAECAAAAAAAAAAEIAAAAAAQAAAIAABADAAAABAAAAAA=</HashCode>
      <FileName>CustomerFacility.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Customer" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Electrode">
    <Position X="1" Y="4" Width="2.5" />
    <AssociationLine Name="MetalChemicalComposition" Type="Miller.WMS.Domain.MetalChemicalComposition" FixedFromPoint="true">
      <Path>
        <Point X="3.5" Y="5.125" />
        <Point X="7.25" Y="5.125" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.154" Y="-0.411" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AEACAAAAAAAAAAAAAgAAAACAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Electrode.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="MetalChemicalComposition" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeClassification">
    <Position X="7.25" Y="13.5" Width="2.75" />
    <Members>
      <Property Name="MetalChemicalCompositionId" Hidden="true" />
      <Property Name="SpecificationId" Hidden="true" />
      <Property Name="TungstenElectrodeClassificationId" Hidden="true" />
    </Members>
    <AssociationLine Name="Specification" Type="Miller.WMS.Domain.Specification" FixedToPoint="true">
      <Path>
        <Point X="10" Y="14.188" />
        <Point X="12.5" Y="14.188" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="CoveringType" Type="Miller.WMS.Domain.ElectrodeCoveringType" FixedToPoint="true">
      <Path>
        <Point X="7.25" Y="15.188" />
        <Point X="6" Y="15.188" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="TungstenElectrodeClassification" Type="Miller.WMS.Domain.TungstenElectrodeClassification" FixedToPoint="true">
      <Path>
        <Point X="7.25" Y="14.562" />
        <Point X="3.25" Y="14.562" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.245" Y="0.142" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="MetalChemicalComposition" Type="Miller.WMS.Domain.MetalChemicalComposition" FixedToPoint="true">
      <Path>
        <Point X="8.562" Y="13.5" />
        <Point X="8.562" Y="8.411" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>gkACAAAAgAAAEBAAAgQAAAAiAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>ElectrodeClassification.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Specification" />
      <Property Name="CoveringType" />
      <Property Name="TungstenElectrodeClassification" />
      <Property Name="MetalChemicalComposition" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeClassificationCurrentType" Collapsed="true">
    <Position X="9" Y="20.75" Width="3" />
    <Members>
      <Property Name="ElectrodeClassificationId" Hidden="true" />
    </Members>
    <AssociationLine Name="ElectrodeClassification" Type="Miller.WMS.Domain.ElectrodeClassification">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.06" Y="0.371" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="CurrentType" Type="Miller.WMS.Domain.CurrentType">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.112" Y="0.117" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAIAAAAgAAAAAAAAAAAAAAAAAAAEAAAAAA=</HashCode>
      <FileName>ElectrodeClassificationHasCurrentType.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="ElectrodeClassification" />
      <Property Name="CurrentType" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeClassificationHasWeldingPosition" Collapsed="true">
    <Position X="5.25" Y="11" Width="3" />
    <Members>
      <Property Name="ElectrodeClassificationId" Hidden="true" />
    </Members>
    <AssociationLine Name="ElectrodeClassification" Type="Miller.WMS.Domain.ElectrodeClassification" ManuallyRouted="true" FixedToPoint="true">
      <Path>
        <Point X="8.062" Y="11.562" />
        <Point X="8.062" Y="13.5" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.024" Y="0.336" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAIAAAAgAAAAAAAAABAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>ElectrodeClassificationHasWeldingPosition.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="ElectrodeClassification" />
      <Property Name="WeldingPosition" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeClassificationHasWeldingProcess" Collapsed="true">
    <Position X="9.75" Y="16.25" Width="2.75" />
    <Members>
      <Property Name="ElectrodeClassificationId" Hidden="true" />
    </Members>
    <AssociationLine Name="ElectrodeClassification" Type="Miller.WMS.Domain.ElectrodeClassification">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.106" Y="0.149" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAIAAAAgAAAAAAAAAAAAAAAQAAAAAAAAAA=</HashCode>
      <FileName>ElectrodeClassificationHasWeldingProcess.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="ElectrodeClassification" />
      <Property Name="WeldingProcess" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeDiameter">
    <Position X="50" Y="3.5" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAACAAAAACAAAAAAAAAAAAAAAAAAAAAAAAABAAAAQAA=</HashCode>
      <FileName>ElectrodeDiameter.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="ElectrodeType" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeHasElectrodeClassification">
    <Position X="0.5" Y="12.75" Width="3.25" />
    <AssociationLine Name="Electrode" Type="Miller.WMS.Domain.Electrode" FixedFromPoint="true" FixedToPoint="true">
      <Path>
        <Point X="1.812" Y="12.75" />
        <Point X="1.812" Y="12.375" />
        <Point X="1.812" Y="12.375" />
        <Point X="1.812" Y="5.488" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="ElectrodeClassification" Type="Miller.WMS.Domain.ElectrodeClassification" FixedFromPoint="true">
      <Path>
        <Point X="3.75" Y="13.812" />
        <Point X="7.25" Y="13.812" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAIAAAAgAAAAAAAAAAAAkAAAAAAAAAAAAA=</HashCode>
      <FileName>ElectrodeHasElectrodeClassification.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Electrode" />
      <Property Name="ElectrodeClassification" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeMadeByManufacturer">
    <Position X="33" Y="3.25" Width="2.75" />
    <AssociationLine Name="ManufacturerFacility" Type="Miller.WMS.Domain.ManufacturerFacility" FixedFromPoint="true">
      <Path>
        <Point X="35.75" Y="4.438" />
        <Point X="38.25" Y="4.438" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.122" Y="-0.343" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAgAAAAAAAAAAAAAAAAAAAAAAAEAAkAEAAQAAAAAAAA=</HashCode>
      <FileName>ElectrodeMadeByManufacturer.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Electrode" />
      <Property Name="Manufacturer" />
      <Property Name="ManufacturerFacility" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.Equipment">
    <Position X="42.5" Y="29.5" Width="1.5" />
    <Members>
      <Property Name="ManufacturerId" Hidden="true" />
    </Members>
    <AssociationLine Name="Manufacturer" Type="Miller.WMS.Domain.Manufacturer">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.339" Y="0.025" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>ABgCAAAEAAAAAAAAAAAAAAAAAAEAAAAAAQgAAAAAAAA=</HashCode>
      <FileName>Equipment.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Manufacturer" />
      <Property Name="Type" />
      <Property Name="SubType" />
      <Property Name="Status" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.EquipmentHasElectrodeClassification">
    <Position X="8" Y="29.75" Width="3.25" />
    <Members>
      <Property Name="ElectrodeClassificationId" Hidden="true" />
      <Property Name="EquipmentId" Hidden="true" />
    </Members>
    <AssociationLine Name="Equipment" Type="Miller.WMS.Domain.Equipment">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.071" Y="-0.41" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="ElectrodeClassification" Type="Miller.WMS.Domain.ElectrodeClassification">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-0.02" Y="0.61" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAIAAAAgAAAAAQAIAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>EquipmentHasElectrodeClassification.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Equipment" />
      <Property Name="ElectrodeClassification" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.Facility">
    <Position X="37" Y="22" Width="1.5" />
    <AssociationLine Name="Organization" Type="Miller.WMS.Domain.Organization" FixedFromPoint="true">
      <Path>
        <Point X="37.938" Y="22" />
        <Point X="37.938" Y="21.68" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.057" Y="0.07" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAASAAAAAAAAAAAAAAAgAAQAAQAAAAACAAAAAAAAIAA=</HashCode>
      <FileName>Facility.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Organization" />
    </ShowAsAssociation>
    <ShowAsCollectionAssociation>
      <Property Name="UserFacilityRoles" />
    </ShowAsCollectionAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.FacilityAreaLevelOne">
    <Position X="33.75" Y="22.5" Width="2.5" />
    <Members>
      <Property Name="FacilityId" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAAKAAAAAAAAAAIAAAAAAAAAAQAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>FacilityAreaLevelOne.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Facility" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.FacilityAreaLevelThree">
    <Position X="27.25" Y="25.75" Width="2.25" />
    <Members>
      <Property Name="FacilityAreaLevelTwoId" Hidden="true" />
    </Members>
    <AssociationLine Name="FacilityAreaLevelTwo" Type="Miller.WMS.Domain.FacilityAreaLevelTwo" FixedFromPoint="true">
      <Path>
        <Point X="29.5" Y="26.625" />
        <Point X="31.25" Y="26.625" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.255" Y="-0.345" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAAAAAAAAABAAAAAQAAAAAAABAAAAAAAAA=</HashCode>
      <FileName>FacilityAreaLevelThree.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="FacilityAreaLevelTwo" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.FacilityAreaLevelTwo">
    <Position X="31.25" Y="26" Width="3.75" />
    <Members>
      <Property Name="FacilityAreaLevelOneId" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAAAAACAAAAAAAAAQAAAAAAAAAAIAAAAAA=</HashCode>
      <FileName>FacilityAreaLevelTwo.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="FacilityAreaLevelOne" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Flux">
    <Position X="54.5" Y="10.25" Width="2.75" />
    <Members>
      <Property Name="FluxChemicalCompositionId" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACAAACAAAAAQAAAAAAAACAAAAAAAAAAAQAAAAQAAE=</HashCode>
      <FileName>Flux.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="FluxChemicalComposition" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Gas">
    <Position X="21.5" Y="24.75" Width="2.5" />
    <Members>
      <Property Name="GasChemicalCompositionId" Hidden="true" />
      <Property Name="GasClassificationId" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAAAABAAAQABAQAAAAAAAAAAAAIAAAAAAA=</HashCode>
      <FileName>Gas.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="GasClassification" />
      <Property Name="GasChemicalComposition" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.GasChemicalComposition">
    <Position X="20.75" Y="27" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAAMAAAAAAAAAAAAAAAAQAAIAAAAAAAAAA=</HashCode>
      <FileName>GasChemicalComposition.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Miller.WMS.Domain.GasClassification">
    <Position X="23.25" Y="27.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAACQEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>GasClassification.cs</FileName>
    </TypeIdentifier>
    <Lollipop Orientation="Left" Position="0.1" />
  </Class>
  <Class Name="Miller.WMS.Domain.IssuingOrganization">
    <Position X="19" Y="13.75" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAACAABAAAAggAAAAAAAAAQAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>IssuingOrganization.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Miller.WMS.Domain.Manufacturer">
    <Position X="42.5" Y="3.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAAAAAAEAAAAAQAAAAAAAAAAQAAAAAAAAA=</HashCode>
      <FileName>Manufacturer.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Type" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.ManufacturerFacility">
    <Position X="38.25" Y="4.25" Width="2.5" />
    <AssociationLine Name="Manufacturer" Type="Miller.WMS.Domain.Manufacturer" ManuallyRouted="true" FixedToPoint="true">
      <Path>
        <Point X="40.75" Y="4.938" />
        <Point X="42.5" Y="4.938" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAgCAAAAAAAAAAAIAAAAAAQAAAMAAAADAAAABAAAAAA=</HashCode>
      <FileName>ManufacturerFacility.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Manufacturer" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Material">
    <Position X="21.5" Y="9" Width="2.5" />
    <Members>
      <Property Name="FulfilledById" Hidden="true" />
    </Members>
    <AssociationLine Name="FulfilledBy" Type="Miller.WMS.Domain.Material" FixedFromPoint="true" FixedToPoint="true">
      <Path>
        <Point X="24" Y="10.937" />
        <Point X="24.25" Y="10.937" />
        <Point X="24.25" Y="11.688" />
        <Point X="24" Y="11.688" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AMAiAAAAQEGAAAAAgAIAAAAAAAAAAgAAEQAAQAkAIAA=</HashCode>
      <FileName>Material.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="FulfilledBy" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.MaterialSubstitution">
    <Position X="14.75" Y="9.5" Width="2.25" />
    <AssociationLine Name="Specification" Type="Miller.WMS.Domain.Specification">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.036" Y="0.891" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AEQCAAAAAAAAABAAAgQAAAAAAAAAAAAAABAAAIAAAAA=</HashCode>
      <FileName>MaterialSubstitution.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Specification" />
      <Property Name="Material" />
      <Property Name="SubstitutedBy" />
      <Property Name="ApprovalType" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.MetalChemicalComposition">
    <Position X="7.25" Y="5" Width="2.75" />
    <TypeIdentifier>
      <HashCode>AAACAAAAiAAAAAgMAAAASAQAAABCCAAAAAAIAAAAAAA=</HashCode>
      <FileName>MetalChemicalComposition.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Miller.WMS.Domain.Organization">
    <Position X="37.25" Y="20" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAACACAEAAAAAAAAAAAAAAQAAAAAQAAAAAAAAgAAAAA=</HashCode>
      <FileName>Organization.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="IndustryType" />
      <Property Name="Status" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Specification">
    <Position X="12.5" Y="12.5" Width="2.75" />
    <AssociationLine Name="SpecificationType" Type="Miller.WMS.Domain.SpecificationType" FixedToPoint="true">
      <Path>
        <Point X="15.25" Y="14.562" />
        <Point X="16.5" Y="14.562" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="SupersededSpec" Type="Miller.WMS.Domain.Specification" FixedFromPoint="true" FixedToPoint="true">
      <Path>
        <Point X="15.25" Y="13.688" />
        <Point X="15.5" Y="13.688" />
        <Point X="15.5" Y="12.812" />
        <Point X="15.25" Y="12.812" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.312" Y="-0.742" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAACAAAEAAAiAFIAAAAgAAAAAAAAAAAAAAAAAAIAAEI=</HashCode>
      <FileName>Specification.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="SpecificationType" />
      <Property Name="IssuingOrganization" />
      <Property Name="Status" />
      <Property Name="SupersededSpec" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.SupplementalFillerMetal">
    <Position X="49.5" Y="18.75" Width="3.25" />
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAA=</HashCode>
      <FileName>SupplementalFillerMetal.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Miller.WMS.Domain.TungstenElectrodeClassification">
    <Position X="0.5" Y="14.25" Width="2.75" />
    <TypeIdentifier>
      <HashCode>AAACAQAAAAAAAAgAAAQAAAAAAAAAAAAAAIAAgAAAgAA=</HashCode>
      <FileName>TungstenElectrodeClassification.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Miller.WMS.Domain.User">
    <Position X="40" Y="19.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAASAAAAACAAAAAAAAAAAAQAAQAAAAAAAAAAAAAAIAA=</HashCode>
      <FileName>User.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Organization" />
    </ShowAsAssociation>
    <ShowAsCollectionAssociation>
      <Property Name="UserFacilityRoles" />
    </ShowAsCollectionAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.UserFacilityRole">
    <Position X="40.25" Y="22.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>EAAJAAAAAAAAAAIAAAAAAAgAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>UserFacilityRole.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="User" />
      <Property Name="Facility" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.ANumberElectrode">
    <Position X="12.5" Y="7.5" Width="2.25" />
    <Members>
      <Property Name="MetalChemicalCompositionId" Hidden="true" />
      <Property Name="SpecificationId" Hidden="true" />
    </Members>
    <AssociationLine Name="Specification" Type="Miller.WMS.Domain.Specification" FixedFromPoint="true" FixedToPoint="true">
      <Path>
        <Point X="13.438" Y="8.796" />
        <Point X="13.438" Y="9.171" />
        <Point X="13.438" Y="9.171" />
        <Point X="13.438" Y="12.5" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.114" Y="0.134" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="MetalChemicalComposition" Type="Miller.WMS.Domain.MetalChemicalComposition">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.336" Y="-0.446" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AEACAAAAAAAAABAAAgAAAAQAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>ANumberElectrode.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Specification" />
      <Property Name="MetalChemicalComposition" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.FluxClass">
    <Position X="55" Y="5.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>FluxClass.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Miller.WMS.Domain.FluxHasFluxClass" Collapsed="true">
    <Position X="54.75" Y="7.75" Width="1.5" />
    <Members>
      <Property Name="FluxClassId" Hidden="true" />
      <Property Name="FluxId" Hidden="true" />
    </Members>
    <AssociationLine Name="Flux" Type="Miller.WMS.Domain.Flux" FixedToPoint="true">
      <Path>
        <Point X="56" Y="8.312" />
        <Point X="56" Y="10.25" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-0.606" Y="0.129" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAQAAAAAwAgAAAAAAAAAAAAA=</HashCode>
      <FileName>FluxHasFluxClass.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Flux" />
      <Property Name="FluxClass" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.WorkCenter">
    <Position X="27.75" Y="21.5" Width="4" />
    <Members>
      <Property Name="FacilityAreaLevelOneId" Hidden="true" />
      <Property Name="FacilityAreaLevelThreeId" Hidden="true" />
      <Property Name="FacilityAreaLevelTwoId" Hidden="true" />
      <Property Name="FacilityId" Hidden="true" />
    </Members>
    <AssociationLine Name="Facility" Type="Miller.WMS.Domain.Facility" FixedToPoint="true">
      <Path>
        <Point X="31.75" Y="22.188" />
        <Point X="37" Y="22.188" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="FacilityAreaLevelOne" Type="Miller.WMS.Domain.FacilityAreaLevelOne" FixedToPoint="true">
      <Path>
        <Point X="31.75" Y="22.625" />
        <Point X="33.75" Y="22.625" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="FacilityAreaLevelTwo" Type="Miller.WMS.Domain.FacilityAreaLevelTwo" FixedToPoint="true">
      <Path>
        <Point X="31.312" Y="22.988" />
        <Point X="31.312" Y="26" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.504" Y="0.337" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="FacilityAreaLevelThree" Type="Miller.WMS.Domain.FacilityAreaLevelThree" FixedFromPoint="true">
      <Path>
        <Point X="28.812" Y="22.988" />
        <Point X="28.812" Y="25.75" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.662" Y="0.141" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAKAAAEAAAgAAICQAABAAQAAAAAAAAAARAIIAAAAAA=</HashCode>
      <FileName>WorkCenter.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Type" />
      <Property Name="Status" />
      <Property Name="Facility" />
      <Property Name="FacilityAreaLevelOne" />
      <Property Name="FacilityAreaLevelTwo" />
      <Property Name="FacilityAreaLevelThree" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.Waveform">
    <Position X="25.25" Y="26.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>IAACAAAAAAAgAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Waveform.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Miller.WMS.Domain.WorkCenterHasElectrode">
    <Position X="28.5" Y="4.25" Width="2.75" />
    <AssociationLine Name="WorkCenter" Type="Miller.WMS.Domain.WorkCenter" FixedToPoint="true">
      <Path>
        <Point X="29.812" Y="5.546" />
        <Point X="29.812" Y="21.5" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAAAAAAgAAAAAAAAAAAAAAAAAAAAkAAAAAAAAAAQAA=</HashCode>
      <FileName>WorkCenterHasElectrode.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="WorkCenter" />
      <Property Name="Electrode" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.WorkCenterHasEquipment">
    <Position X="18" Y="21" Width="2.75" />
    <AssociationLine Name="WorkCenter" Type="Miller.WMS.Domain.WorkCenter" FixedFromPoint="true">
      <Path>
        <Point X="20.75" Y="21.625" />
        <Point X="27.75" Y="21.625" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="Equipment" Type="Miller.WMS.Domain.Equipment" FixedFromPoint="true" FixedToPoint="true">
      <Path>
        <Point X="19.375" Y="22.296" />
        <Point X="19.375" Y="29.75" />
        <Point X="42.5" Y="29.75" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAAAAAAgAAAAAAAAAAAAAQAIAAAAAAAAAAAAAAAQAA=</HashCode>
      <FileName>WorkCenterHasEquipment.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="WorkCenter" />
      <Property Name="Equipment" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.WorkCenterHasGas">
    <Position X="21.25" Y="22" Width="2.75" />
    <TypeIdentifier>
      <HashCode>AAgAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA=</HashCode>
      <FileName>WorkCenterHasGas.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="WorkCenter" />
      <Property Name="Gas" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.WorkCenterHasWaveform" Collapsed="true">
    <Position X="25.75" Y="24.5" Width="2.75" />
    <Members>
      <Property Name="WaveformId" Hidden="true" />
      <Property Name="WorkCenterId" Hidden="true" />
    </Members>
    <AssociationLine Name="WorkCenter" Type="Miller.WMS.Domain.WorkCenter" FixedFromPoint="true">
      <Path>
        <Point X="27.875" Y="24.5" />
        <Point X="27.875" Y="22.988" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAAAAAAgAAAAAAAABAAAAAAAAAAAAAAAAABAAAAQAA=</HashCode>
      <FileName>WorkCenterHasWaveform.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="WorkCenter" />
      <Property Name="Waveform" />
    </ShowAsAssociation>
  </Class>
  <Class Name="Miller.WMS.Domain.FluxChemicalComposition">
    <Position X="51.25" Y="10.25" Width="2" />
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAgAAAAAAAAAQAAAAAAAAAAAAAAAAAACAA=</HashCode>
      <FileName>FluxChemicalComposition.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.FluxChemicalCompositionLimits">
    <Position X="51.25" Y="13" Width="2.5" />
    <Members>
      <Property Name="FluxChemicalCompositionId" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAAAQAAAAAAAAAAAAAAAgAIAAAIAAAQAAA=</HashCode>
      <FileName>FluxChemicalCompositionLimits.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="FluxChemicalComposition" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Enum Name="Miller.WMS.Domain.ElectrodeCoveringType">
    <Position X="3.5" Y="15" Width="2.5" />
    <TypeIdentifier>
      <HashCode>ACAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAABAAAAABA=</HashCode>
      <FileName>ElectrodeCoveringType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.ElectrodeType">
    <Position X="50" Y="5.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAABAAAAAAAAAAgAAIAAAAAAAAAAAAAAA=</HashCode>
      <FileName>ElectrodeType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.EquipmentStatus">
    <Position X="42" Y="26.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAACAAAAAAAABAAAAAAAAAAAAAAAAAgAAAAAAAAAAA=</HashCode>
      <FileName>EquipmentStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.EquipmentSubType">
    <Position X="42.25" Y="32" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAACIAQAgAABAAAAAAAAAAAIBAAAAAAAAAAAABJAAAA=</HashCode>
      <FileName>EquipmentSubType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.EquipmentType">
    <Position X="45.25" Y="30.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>BAAAAAAAgAAAIAAAAAAAAAAAAAAAAIAACAGAAAQAAAA=</HashCode>
      <FileName>EquipmentType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.ManufacturerType">
    <Position X="42.25" Y="1.5" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAQAQAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAA=</HashCode>
      <FileName>ManufacturerType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.MaterialSubstitutionApprovalType">
    <Position X="16" Y="11.75" Width="2.75" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAoAEAAAA=</HashCode>
      <FileName>MaterialSubstitutionApprovalType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.OrganizationIndustryType">
    <Position X="37" Y="17.75" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAEAAAAA=</HashCode>
      <FileName>OrganizationIndustryType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.OrganizationStatus">
    <Position X="33.75" Y="19.25" Width="2.5" />
    <TypeIdentifier>
      <HashCode>AAAACABAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAA=</HashCode>
      <FileName>OrganizationStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.SpecificationStatus">
    <Position X="10.25" Y="12.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAACAAAAAAAAAEAAAAAAAAAAAAAAAAgAAAAAAAAAAA=</HashCode>
      <FileName>SpecificationStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.SpecificationType">
    <Position X="16.5" Y="14.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>BEBAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAEAAA=</HashCode>
      <FileName>SpecificationType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.WeldingPosition">
    <Position X="6.5" Y="8.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAABAAAAAAAAAEAIAAAAAAACAABAAIAAAAAA=</HashCode>
      <FileName>WeldingPosition.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.WeldingProcess">
    <Position X="10.25" Y="17.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AATAAABAAAAAAAAAAAJAAgAAAAAAAAAAAAABAABAACA=</HashCode>
      <FileName>WeldingProcess.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.WorkCenterStatus">
    <Position X="24.75" Y="22.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAACABAAAAAAAAAAAAAAAAAAAAAAAAgAAACAAAAAAA=</HashCode>
      <FileName>WorkCenterStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.WorkCenterType">
    <Position X="28" Y="19.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>BAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAACAAAQAAAA=</HashCode>
      <FileName>WorkCenterType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.CurrentType">
    <Position X="13.25" Y="20.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAACgA=</HashCode>
      <FileName>CurrentType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Font Name="Segoe UI" Size="9" />
</ClassDiagram>