﻿using System;

namespace Miller.WMS.Domain;

public class UserFacilityRole
{
    public Guid UserId { get; set; }
    public User User { get; set; } = null!;
    public Guid FacilityId { get; set; }
    public Facility Facility { get; set; } = null!;
    public string RoleAtFacility { get; set; } = null!;
    // No Id property, but for EF Core join entity, you may want to add a composite key in configuration
}
