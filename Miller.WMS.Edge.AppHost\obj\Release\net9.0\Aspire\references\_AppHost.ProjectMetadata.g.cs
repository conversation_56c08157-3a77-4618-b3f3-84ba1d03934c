// <auto-generated/>

namespace Projects;

#pragma warning disable CS8981 // The type name only contains lower-cased ascii characters. Such names may become reserved for the language.
[global::System.CodeDom.Compiler.GeneratedCode("Aspire.Hosting", null)]
[global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage(Justification = "Generated code.")]
[global::System.Diagnostics.DebuggerDisplay("Type = {GetType().Name,nq}, ProjectPath = {ProjectPath}")]
public class Miller_WMS_Edge_AppHost
#pragma warning restore CS8981
{
    private Miller_WMS_Edge_AppHost() { }
    public static string ProjectPath => """C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.AppHost""";
}
