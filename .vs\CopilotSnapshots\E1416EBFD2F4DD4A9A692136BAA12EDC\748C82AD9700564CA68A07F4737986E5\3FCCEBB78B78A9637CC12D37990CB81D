﻿using System;

namespace Miller.WMS.Domain;

public class Gas : IEntity
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public Guid GasClassificationId { get; set; }
    public GasClassification GasClassification { get; set; } = null!;

    public Guid GasChemicalCompositionId { get; set; }
    public GasChemicalComposition GasChemicalComposition { get; set; } = null!;
}
