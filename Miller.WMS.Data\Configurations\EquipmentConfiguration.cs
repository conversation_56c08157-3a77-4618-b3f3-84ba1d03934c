﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class EquipmentConfiguration : IEntityTypeConfiguration<Equipment>
{
    public void Configure(EntityTypeBuilder<Equipment> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Model)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.Type)
               .HasConversion<int>()
               .IsRequired();

        builder.Property(e => e.SubType)
               .HasConversion<int>()
               .IsRequired();

        builder.Property(e => e.Status)
               .HasConversion<int>()
               .IsRequired();

        builder.HasOne(e => e.Manufacturer)
               .WithMany()
               .HasForeignKey(e => e.ManufacturerId)
               .OnDelete(DeleteBehavior.Restrict);
    }
}
