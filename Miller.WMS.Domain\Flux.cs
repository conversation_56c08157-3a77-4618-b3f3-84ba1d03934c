﻿namespace Miller.WMS.Domain;

public class Flux : IEntity
{
    public Guid Id { get; set; }

    public required string TradeName { get; set; }
    public bool IsDrying { get; set; }
    public string? VendorRecommendationDryingFlux { get; set; }
    public bool IsPenetrationEnhanced { get; set; }
    public Guid FluxChemicalCompositionId { get; set; }
    public FluxChemicalComposition? FluxChemicalComposition { get; set; }
}
