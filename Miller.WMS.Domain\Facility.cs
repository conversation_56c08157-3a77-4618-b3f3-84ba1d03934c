﻿using System;
using Miller.WMS.Domain;

namespace Miller.WMS.Domain;

public class Facility : IEntity
{
    public Guid Id { get; set; }
    public required string Name { get; set; }
    public string? Code { get; set; }
    public string? Address { get; set; }
    public Guid OrganizationId { get; set; }
    public Organization? Organization { get; set; }
    public ICollection<UserFacilityRole> UserFacilityRoles { get; set; } = [];
}