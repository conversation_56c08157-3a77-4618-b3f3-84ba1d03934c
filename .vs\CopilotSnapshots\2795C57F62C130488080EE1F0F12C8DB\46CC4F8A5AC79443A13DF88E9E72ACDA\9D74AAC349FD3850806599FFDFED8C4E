﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class UserFacilityConfiguration : IEntityTypeConfiguration<UserFacilityRole>
{
    public void Configure(EntityTypeBuilder<UserFacilityRole> builder)
    {
        builder.HasKey(e => new { e.UserId, e.FacilityId });

        builder.Property(e => e.RoleAtFacility)
               .HasMaxLength(255)
               .IsRequired();

        builder.HasOne(e => e.User)
               .WithMany(u => u.UserFacilityRoles)
               .HasForeignKey(e => e.UserId)
               .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Facility)
               .WithMany(f => f.UserFacilityRoles)
               .HasForeignKey(e => e.FacilityId)
               .OnDelete(DeleteBehavior.Cascade);
    }
}
