{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["o6lNaEYpB0QdwUFsBAXtaUiZjbAz8gGf6u8sBWwKclU=", "iXQuglaBGG82CWaA/3foADzAHft1btfrE+HOZnwqDQM=", "N6OdN1kMNBy2UKeAopRY9hhXrqBUkTFGdMw5xK7IjzI=", "uPvZTO2q1aYGxuRhhN1eXMIcdRXs4S+JT3s/o/ay/3s=", "hMR5RDgO+Zv35I4pOhBlVZNdUMka71QnZazWW6sdXF0=", "nYyRrEHeu8PAAhU1Lbj9FUv0/pNIBvmhB+yBXzc3v7A=", "y1PA9GYtq7GNyaR1R2nMGfiacwmtQtxXmaITlgyGw0A=", "Ut5XEH45woNV03Ao6G0UOHuqKjyzTmuVaouP5ifSZs8=", "laWBWttSs0q8Mg0wrrIpHX7271ZkIBdvVBlV/Nl7AKA=", "pNJ3CTYgI+NA5Du7M0E7pxIBBc+uXmFag1ob2Z6Cjrw=", "U2Xvg4r1+Anetl/JEWL9LVtvZUg1KOt9e2qorueqtNg=", "iAnzdY7Uv/AjW47i8z//Hr8ZkSFPjmuP5apa+J3zS4w=", "/cRZDgso853nr1YTBpAdh043Dh3pwupKeHEWo+WpnYk=", "lJoEK6LekbPf7b5GkRXZvKeExv/YmJfr+WMUnjGYydg=", "vzcU9/8+7LmsZc+8gjPhdFqrXCA20K+J/z08wIW3aK0=", "3mfSlC5bjZw+7d1XPDzOTS4B+u2zMqQdjdUOPFzmemI=", "3n8rTq5c1elnRjlZo3GWHISS/S0qiUt3NY6hqKr8Qd4=", "pFpFyFJAf94A0Yw7lTJZn8D/Hfx8iXj/lKaNN8oGs18=", "VpjECc0N6RxPqA8T5AkZlKlQKPJwhMtKf0fZ276DEDA=", "F3VbY0L9xT2pkrZnEO/M3hfF967dheWB09wWsaTbFUQ=", "nGce3cc86uhTopZjai3jcs1SVfurXl4Tuk1bRVrJ5rQ=", "moKscs0i8he6iqRgl6+jW5jMkZwa0960DRRAOGd1mzU=", "JD55KGK6pfZYl1nSQ+qiSrLJ0c7iO2cXjTewhVK7PQA=", "jNSzuPGjRB8m/s2e4DX6CQpZoMz68953K2Um16VtYbU=", "ZxGrt+RQKm3Y7N2D+S5WiJNnUSRW1CtNIACFGofzMSs=", "jcHkPOWeFjRjlIczxC96R3j7OgVT5W4nmVUdSJ03Zos=", "PQ1qWEPLGLwuTBIgkyf8lg8I5o1e18vuWDQlXyRjwFQ=", "t0x+mpAbnvAUVQge4ksu2cB3ozQsT1lJG0EjuWRXWUw=", "WsL14BMTDSGGGkM+rCpK7oq0pINZEV0DAb8GRmyAN+I=", "g9hrhbp3xRUujBXAaqUs9T1Xx4yk4MXQreyRGBer3C8=", "r+rDfg9nFdGeNrN5hq+7boIrxO/ouUZG+c28gc02Y7Q=", "dBfuBeJedthzJv4prag2iYwvnl62tQowvk1rSxCgmi0=", "r+8ZhV217cdxwGV95+XhZJmbRbaEaPDqSWQrQERY/w4=", "zWbbvjf/YaHpzma1EObeMuHFoe1QbuMm4urgFwD6ntc=", "xJh3Am9CwCZ09TR0R6nrwNfY+yLSogA9vB6+mA17rAc=", "Y3d36FYpWJkUdvGQuCrEt5DXSiWlMRhPNFk4C1IBvc4=", "wuhLEdJCqbW3ecEy2iNmt0CeEZLvTUhR3uk4Z8aFgfo=", "UwUc+PFIV07vpkkrIbpO33bo4LPaUI8b2wzM0ViWj4s=", "UTozOCIqUF1874YRRlcXF+hEE1y3eau9yVaFWjBG4aU=", "gEadEEJKgsXxDxQEvn6D+sK2ER2X/9Hax2bU+doUkT8=", "KZu4lJFgS4mpVuTz1vhiAfg8FA4VCJsjKhPhT2gtSYo=", "IJ2ZZOYkE/Anfj5F/eH1wleJhCR33F1X5T5UtQ3a0ak=", "u1sIBiZ1ot2dCEAkKrr2VRchnS9e8N27ynrmpdwGwww=", "bpF0YOJ+HolKkjbfir5zgToJNiMvfD/lr/pQZW51j8A=", "ACjrNsIcyTbe+LbsmtUjTlQ64MHJf6oEM3wXS3o3Fhg=", "kosAcMha2JiMwUY0QvSMVq9PXnA1qNG1dhMJWQb0X50=", "ygDnLMr9+sNEEUBKHegZEhV7vWWZ7idJpqSn1r2Ls0g="], "CachedAssets": {"ygDnLMr9+sNEEUBKHegZEhV7vWWZ7idJpqSn1r2Ls0g=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\q4h1had4qc-ug9uonmnr1.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Computed", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "Miller.WMS.Edge.Web#[.{fingerprint=ug9uonmnr1}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Miller.WMS.Edge.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kkdzock9gm", "Integrity": "dHe1vjIYjc5hErECvMhqbI3pHlMFu9E54XI/Ymd/8DE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Miller.WMS.Edge.Web.bundle.scp.css", "FileLength": 1731, "LastWriteTime": "2025-08-13T01:02:00.869918+00:00"}, "kosAcMha2JiMwUY0QvSMVq9PXnA1qNG1dhMJWQb0X50=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\emjngf5i2u-ug9uonmnr1.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Computed", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "Miller.WMS.Edge.Web#[.{fingerprint=ug9uonmnr1}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Miller.WMS.Edge.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kkdzock9gm", "Integrity": "dHe1vjIYjc5hErECvMhqbI3pHlMFu9E54XI/Ymd/8DE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Miller.WMS.Edge.Web.styles.css", "FileLength": 1731, "LastWriteTime": "2025-08-13T01:02:00.79577+00:00"}, "ACjrNsIcyTbe+LbsmtUjTlQ64MHJf6oEM3wXS3o3Fhg=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\b1bks4wm49-r37jpkscte.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=r37jpkscte}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1vtrgsyhbw", "Integrity": "E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55527, "LastWriteTime": "2025-08-13T01:02:00.9025491+00:00"}, "bpF0YOJ+HolKkjbfir5zgToJNiMvfD/lr/pQZW51j8A=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\9oew4imh8l-ze3dr5b7df.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=ze3dr5b7df}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2kg58l0pnq", "Integrity": "Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 26285, "LastWriteTime": "2025-08-13T01:02:00.8774439+00:00"}, "u1sIBiZ1ot2dCEAkKrr2VRchnS9e8N27ynrmpdwGwww=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\wdlrvgww7e-k72fsduyas.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=k72fsduyas}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f8y9r0oetv", "Integrity": "ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64493, "LastWriteTime": "2025-08-13T01:02:00.8515019+00:00"}, "IJ2ZZOYkE/Anfj5F/eH1wleJhCR33F1X5T5UtQ3a0ak=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\x92en9a9mp-cwuvm2sdc3.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=cwuvm2sdc3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3uebi1j6we", "Integrity": "28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 38707, "LastWriteTime": "2025-08-13T01:02:00.8804448+00:00"}, "KZu4lJFgS4mpVuTz1vhiAfg8FA4VCJsjKhPhT2gtSYo=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\o03tar9ax1-eve9uzuztn.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=eve9uzuztn}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "43czolro0z", "Integrity": "EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56387, "LastWriteTime": "2025-08-13T01:02:00.8617743+00:00"}, "gEadEEJKgsXxDxQEvn6D+sK2ER2X/9Hax2bU+doUkT8=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\k82aaqjmgf-n5tfi6zt97.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=n5tfi6zt97}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fd8at7e5rf", "Integrity": "Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 28200, "LastWriteTime": "2025-08-13T01:02:00.853509+00:00"}, "UTozOCIqUF1874YRRlcXF+hEE1y3eau9yVaFWjBG4aU=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\oqgoo69acv-wf6sfai52w.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=wf6sfai52w}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ktgldfzmo5", "Integrity": "aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64236, "LastWriteTime": "2025-08-13T01:02:00.9140806+00:00"}, "UwUc+PFIV07vpkkrIbpO33bo4LPaUI8b2wzM0ViWj4s=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\b2gqtoi2v8-ja11lcg8ur.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=ja11lcg8ur}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i8nhkk7sb8", "Integrity": "HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 38013, "LastWriteTime": "2025-08-13T01:02:00.8871363+00:00"}, "wuhLEdJCqbW3ecEy2iNmt0CeEZLvTUhR3uk4Z8aFgfo=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\dkozbjx6t1-okq9zf051y.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=okq9zf051y}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3zm0g72duv", "Integrity": "T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86520, "LastWriteTime": "2025-08-13T01:02:00.866773+00:00"}, "Y3d36FYpWJkUdvGQuCrEt5DXSiWlMRhPNFk4C1IBvc4=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\sw7twzm0gz-252a5wndhh.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=252a5wndhh}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ktya8jz5ov", "Integrity": "yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 32944, "LastWriteTime": "2025-08-13T01:02:00.895312+00:00"}, "xJh3Am9CwCZ09TR0R6nrwNfY+yLSogA9vB6+mA17rAc=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\iqna1yvk48-fxquxrv84i.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=fxquxrv84i}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xhofi<PERSON><PERSON>", "Integrity": "xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92278, "LastWriteTime": "2025-08-13T01:02:00.8831375+00:00"}, "zWbbvjf/YaHpzma1EObeMuHFoe1QbuMm4urgFwD6ntc=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\alht8ht9n7-iy2auvubsp.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=iy2auvubsp}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42jbnex9ve", "Integrity": "PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 54528, "LastWriteTime": "2025-08-13T01:02:00.8494954+00:00"}, "r+8ZhV217cdxwGV95+XhZJmbRbaEaPDqSWQrQERY/w4=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\dir3evalzp-ft3s53vfgj.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uom9ela1zq", "Integrity": "4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91866, "LastWriteTime": "2025-08-13T01:02:00.9005487+00:00"}, "dBfuBeJedthzJv4prag2iYwvnl62tQowvk1rSxCgmi0=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\3td72ajpnc-c63t5i9ira.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=c63t5i9ira}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a66bztmsz2", "Integrity": "AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 31142, "LastWriteTime": "2025-08-13T01:02:00.847489+00:00"}, "r+rDfg9nFdGeNrN5hq+7boIrxO/ouUZG+c28gc02Y7Q=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\h627czx73f-hrwsygsryq.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2knqut3gi", "Integrity": "YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114877, "LastWriteTime": "2025-08-13T01:02:00.8851374+00:00"}, "g9hrhbp3xRUujBXAaqUs9T1Xx4yk4MXQreyRGBer3C8=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\iunmkf7vrb-ynyaa8k90p.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=ynyaa8k90p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ws8e397h8g", "Integrity": "EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33667, "LastWriteTime": "2025-08-13T01:02:00.9111147+00:00"}, "WsL14BMTDSGGGkM+rCpK7oq0pINZEV0DAb8GRmyAN+I=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\dpx8dg3hki-v0zj4ognzu.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77t5enldb7", "Integrity": "Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91960, "LastWriteTime": "2025-08-13T01:02:00.8913136+00:00"}, "t0x+mpAbnvAUVQge4ksu2cB3ozQsT1lJG0EjuWRXWUw=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\kh52fnj1t5-43atpzeawx.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=43atpzeawx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ny7oqyylde", "Integrity": "sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 31123, "LastWriteTime": "2025-08-13T01:02:00.8222282+00:00"}, "PQ1qWEPLGLwuTBIgkyf8lg8I5o1e18vuWDQlXyRjwFQ=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\wwxjh0tqb5-pj5nd1wqec.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glev5uv9kg", "Integrity": "pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 114902, "LastWriteTime": "2025-08-13T01:02:00.866773+00:00"}, "jcHkPOWeFjRjlIczxC96R3j7OgVT5W4nmVUdSJ03Zos=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\1bxgs52le7-zub09dkrxp.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=zub09dkrxp}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ndo96zskmb", "Integrity": "q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33770, "LastWriteTime": "2025-08-13T01:02:00.8399348+00:00"}, "ZxGrt+RQKm3Y7N2D+S5WiJNnUSRW1CtNIACFGofzMSs=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\8qil2ml6xf-nvvlpmu67g.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0gru265j2n", "Integrity": "cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24456, "LastWriteTime": "2025-08-13T01:02:00.8327437+00:00"}, "jNSzuPGjRB8m/s2e4DX6CQpZoMz68953K2Um16VtYbU=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\356jgs3b6q-keugtjm085.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=keugtjm085}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b271r4kg0j", "Integrity": "A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11132, "LastWriteTime": "2025-08-13T01:02:00.8419421+00:00"}, "JD55KGK6pfZYl1nSQ+qiSrLJ0c7iO2cXjTewhVK7PQA=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\mdrj46vdyz-j5mq2jizvt.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pnmw3mh9ht", "Integrity": "tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44237, "LastWriteTime": "2025-08-13T01:02:00.8379287+00:00"}, "moKscs0i8he6iqRgl6+jW5jMkZwa0960DRRAOGd1mzU=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\k2ikjqw9ce-d4r6k3f320.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=d4r6k3f320}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a9e8v2fr2b", "Integrity": "0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 12194, "LastWriteTime": "2025-08-13T01:02:00.8252287+00:00"}, "nGce3cc86uhTopZjai3jcs1SVfurXl4Tuk1bRVrJ5rQ=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\8acc8zdz5p-c2oey78nd0.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oiolxl3gck", "Integrity": "oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24487, "LastWriteTime": "2025-08-13T01:02:00.8327437+00:00"}, "F3VbY0L9xT2pkrZnEO/M3hfF967dheWB09wWsaTbFUQ=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\j9m9vubjhm-wl58j5mj3v.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=wl58j5mj3v}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2dzz7zk1je", "Integrity": "Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11153, "LastWriteTime": "2025-08-13T01:02:00.8242288+00:00"}, "VpjECc0N6RxPqA8T5AkZlKlQKPJwhMtKf0fZ276DEDA=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\zaq01k6aj8-r4e9w2rdcm.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21g01g2f66", "Integrity": "JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44266, "LastWriteTime": "2025-08-13T01:02:00.8287451+00:00"}, "pFpFyFJAf94A0Yw7lTJZn8D/Hfx8iXj/lKaNN8oGs18=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\jvfnk970r6-gye83jo8yx.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=gye83jo8yx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3pw7iimyz", "Integrity": "DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 12244, "LastWriteTime": "2025-08-13T01:02:00.819228+00:00"}, "3n8rTq5c1elnRjlZo3GWHISS/S0qiUt3NY6hqKr8Qd4=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\v0yt5xmjoa-jd9uben2k1.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ldvkm706vq", "Integrity": "J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15028, "LastWriteTime": "2025-08-13T01:02:00.8122203+00:00"}, "3mfSlC5bjZw+7d1XPDzOTS4B+u2zMqQdjdUOPFzmemI=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\41hwvam6ld-q9ht133ko3.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=q9ht133ko3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d645a0veuj", "Integrity": "ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3235, "LastWriteTime": "2025-08-13T01:02:00.8059172+00:00"}, "vzcU9/8+7LmsZc+8gjPhdFqrXCA20K+J/z08wIW3aK0=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\k723h2movt-ee0r1s7dh0.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kqk14ew2nl", "Integrity": "PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25726, "LastWriteTime": "2025-08-13T01:02:00.8419421+00:00"}, "lJoEK6LekbPf7b5GkRXZvKeExv/YmJfr+WMUnjGYydg=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\eovc2x63tg-rxsg74s51o.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rxsg74s51o}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fjx614p1f2", "Integrity": "tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3395, "LastWriteTime": "2025-08-13T01:02:00.8317439+00:00"}, "/cRZDgso853nr1YTBpAdh043Dh3pwupKeHEWo+WpnYk=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\jpxe5yqdj9-fsbi9cje9m.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ggfb0v5ylw", "Integrity": "ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12568, "LastWriteTime": "2025-08-13T01:02:00.8287451+00:00"}, "iAnzdY7Uv/AjW47i8z//Hr8ZkSFPjmuP5apa+J3zS4w=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\7ixhcdsvi5-tmc1g35s3z.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=tmc1g35s3z}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8wl3mrbh96", "Integrity": "mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3204, "LastWriteTime": "2025-08-13T01:02:00.869918+00:00"}, "U2Xvg4r1+Anetl/JEWL9LVtvZUg1KOt9e2qorueqtNg=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\n8b8oxdoha-fvhpjtyr6v.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5b7ig2cj79", "Integrity": "fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25714, "LastWriteTime": "2025-08-13T01:02:00.853509+00:00"}, "pNJ3CTYgI+NA5Du7M0E7pxIBBc+uXmFag1ob2Z6Cjrw=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\3mp2sejyk9-qesaa3a1fm.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=qesaa3a1fm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yhemft0x44", "Integrity": "GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3406, "LastWriteTime": "2025-08-13T01:02:00.7937616+00:00"}, "laWBWttSs0q8Mg0wrrIpHX7271ZkIBdvVBlV/Nl7AKA=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\s19q5lb0r9-cosvhxvwiu.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f178oapzb7", "Integrity": "XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 14093, "LastWriteTime": "2025-08-13T01:02:00.8399348+00:00"}, "Ut5XEH45woNV03Ao6G0UOHuqKjyzTmuVaouP5ifSZs8=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\3tw20sx7mb-22vffe00uq.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=22vffe00uq}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ki2uzdiv5", "Integrity": "cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 6108, "LastWriteTime": "2025-08-13T01:02:00.8297446+00:00"}, "y1PA9GYtq7GNyaR1R2nMGfiacwmtQtxXmaITlgyGw0A=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\mk8s5dumf5-ausgxo2sd3.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnst782kog", "Integrity": "ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 33061, "LastWriteTime": "2025-08-13T01:02:00.8252287+00:00"}, "nYyRrEHeu8PAAhU1Lbj9FUv0/pNIBvmhB+yBXzc3v7A=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\0ii28lkdjo-xvp3kq03qx.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=xvp3kq03qx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z7y6dfz9j3", "Integrity": "vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6975, "LastWriteTime": "2025-08-13T01:02:00.8277444+00:00"}, "hMR5RDgO+Zv35I4pOhBlVZNdUMka71QnZazWW6sdXF0=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\wtjtnptcj1-aexeepp0ev.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3latwtrz94", "Integrity": "1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 14072, "LastWriteTime": "2025-08-13T01:02:00.8222282+00:00"}, "uPvZTO2q1aYGxuRhhN1eXMIcdRXs4S+JT3s/o/ay/3s=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\xq3d8yg16u-sejl45xvog.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=sejl45xvog}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ru0tedr80l", "Integrity": "k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 6106, "LastWriteTime": "2025-08-13T01:02:00.8162274+00:00"}, "N6OdN1kMNBy2UKeAopRY9hhXrqBUkTFGdMw5xK7IjzI=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\66bpgrr88p-c2jlpeoesf.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bpa5g0wlhg", "Integrity": "keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 33080, "LastWriteTime": "2025-08-13T01:02:00.8831375+00:00"}, "iXQuglaBGG82CWaA/3foADzAHft1btfrE+HOZnwqDQM=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\ma60horqfq-t1cqhe9u97.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=t1cqhe9u97}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4t5zwq0ztq", "Integrity": "KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6973, "LastWriteTime": "2025-08-13T01:02:00.8719277+00:00"}, "o6lNaEYpB0QdwUFsBAXtaUiZjbAz8gGf6u8sBWwKclU=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\17j0rgum0i-d3h8l9wove.gz", "SourceId": "Miller.WMS.Edge.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Edge.Web", "RelativePath": "app#[.{fingerprint=d3h8l9wove}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fcjvn85lhy", "Integrity": "UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Edge.Web\\wwwroot\\app.css", "FileLength": 1511, "LastWriteTime": "2025-08-13T01:02:00.869918+00:00"}}, "CachedCopyCandidates": {}}