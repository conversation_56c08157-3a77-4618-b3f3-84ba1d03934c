﻿namespace Miller.WMS.Domain;

public class TungstenElectrodeClassification
{
    public int Id { get; set; }

    public string Classification { get; set; } = null!;
    public string? PrincipalOxide { get; set; }

    public decimal? MinMass { get; set; }         // (5,4)
    public decimal? MaxMass { get; set; }         // (5,4)
    public decimal? MaxImpurities { get; set; }   // (5,4)
    public string Color { get; set; } = null!;
}
