﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class SpecificationConfiguration : IEntityTypeConfiguration<Specification>
{
    public void Configure(EntityTypeBuilder<Specification> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.SpecificationType)
               .HasConversion<int>()
               .IsRequired();

        builder.Property(e => e.OtherSpecification)
               .HasMaxLength(255);

        builder.Property(e => e.Code)
               .HasMaxLength(100)
               .IsRequired();

        builder.Property(e => e.Title)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.Description)
               .HasMaxLength(255);

        builder.Property(e => e.Status)
               .HasConversion<int>()
               .IsRequired();

        builder.HasOne(e => e.IssuingOrganization)
               .WithMany()
               .HasForeignKey(e => e.IssuingOrganizationId)
               .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.SupersededSpec)
               .WithMany(e => e.SupersedingSpecs)
               .HasForeignKey(e => e.SupersededSpecId)
               .OnDelete(DeleteBehavior.SetNull);
    }
}
