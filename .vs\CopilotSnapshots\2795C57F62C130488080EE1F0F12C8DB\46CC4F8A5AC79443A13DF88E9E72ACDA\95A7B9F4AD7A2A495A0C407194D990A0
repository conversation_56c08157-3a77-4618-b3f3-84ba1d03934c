﻿namespace Miller.WMS.Domain;

public class User
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Email { get; set; }
    public int OrganizationId { get; set; }
    public Organization Organization { get; set; }
    public ICollection<UserFacilityRole> UserFacilityRoles { get; set; }

    public User()
    {
        Name = string.Empty;
        Email = string.Empty;
        Organization = new Organization();
        UserFacilityRoles = new List<UserFacilityRole>();
    }
}