﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class MaterialSubstitutionConfiguration : IEntityTypeConfiguration<MaterialSubstitution>
{
    public void Configure(EntityTypeBuilder<MaterialSubstitution> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.ApprovalType)
               .HasConversion<int>()
               .IsRequired();

        builder.HasOne(e => e.Specification)
               .WithMany()
               .HasForeignKey(e => e.SpecificationId)
               .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.Material)
               .WithMany()
               .HasForeignKey(e => e.MaterialId)
               .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.SubstitutedBy)
               .WithMany()
               .HasForeignKey(e => e.SubstitutedById)
               .OnDelete(DeleteBehavior.Restrict);
    }
}
