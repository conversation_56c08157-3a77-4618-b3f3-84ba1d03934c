﻿using System;
using System.Collections.Generic;

namespace Miller.WMS.Domain;

public class Equipment : IEntity
{
    public Guid Id { get; set; }

    public Guid ManufacturerId { get; set; }
    public Manufacturer Manufacturer { get; set; } = null!;

    public EquipmentType Type { get; set; }
    public EquipmentSubType SubType { get; set; }
    public string Model { get; set; } = null!;
    public EquipmentStatus Status { get; set; }
}
