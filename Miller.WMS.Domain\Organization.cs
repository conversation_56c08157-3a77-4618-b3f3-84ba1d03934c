﻿using System;
using System.Collections.Generic;

namespace Miller.WMS.Domain;

public class Organization : IEntity
{
    public Guid Id { get; set; }
    public required string Name { get; set; }

    public OrganizationIndustryType? IndustryType { get; set; }
    public OrganizationStatus Status { get; set; }
    public ICollection<Facility> Facilities { get; set; } = [];
    public ICollection<User> Users { get; set; } = [];
}