﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class ElectrodeClassificationWeldingPositionConfiguration : IEntityTypeConfiguration<ElectrodeClassificationHasWeldingPosition>
{
    public void Configure(EntityTypeBuilder<ElectrodeClassificationHasWeldingPosition> builder)
    {
        builder.Has<PERSON><PERSON>(e => new { e.ElectrodeClassificationId, e.WeldingPosition });


        builder.HasOne(e => e.ElectrodeClassification)
               .WithMany()
               .HasF<PERSON>ign<PERSON>ey(e => e.ElectrodeClassificationId)
               .OnDelete(DeleteBehavior.Cascade);

    }
}
