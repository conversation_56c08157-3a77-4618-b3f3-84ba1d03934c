﻿using System;

namespace Miller.WMS.Domain;

public class Material : IEntity
{
    public Guid Id { get; set; }

    public string Spec { get; set; } = null!;

    // Self-reference: when this material has been superseded/fulfilled by a newer material
    public Guid? FulfilledById { get; set; }
    public Material? FulfilledBy { get; set; }

    // Inverse: all older materials that this material supersedes
    public ICollection<Material> SupersededMaterials { get; set; } = [];

    public string? MaterialForm { get; set; }
    public decimal? Hardness { get; set; }              // (5,2)
    public decimal? MaterialDiameter { get; set; }      // (6,3)
    public decimal? MaterialThickness { get; set; }     // (6,3)
    public string BaseMetalGroup { get; set; } = null!;
    public string BaseMetalSubGroup { get; set; } = null!;
    public bool CharpyRequirement { get; set; }
    public decimal? WeldCarbonEquivalent { get; set; }  // (5,2)
    public int? WeldMinYieldStrength { get; set; }

    public string Class { get; set; } = null!;
    public string Grade { get; set; } = null!;
    public string Condition { get; set; } = null!;
    public string? HeatTreatment { get; set; }
    public string? ForgeMaterial { get; set; }
}
