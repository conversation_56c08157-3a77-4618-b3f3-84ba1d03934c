﻿namespace Miller.WMS.Domain;

public class FluxChemicalCompositionLimits : IEntity
{
    public Guid Id { get; set; }

    public int FluxChemicalCompositionId { get; set; }
    public required FluxChemicalComposition FluxChemicalComposition { get; set; }

    public required string ChemicalConstituents { get; set; } // max 255
    public decimal ConstituentLimitMin { get; set; }           // (5,4)
    public decimal ConstituentLimitMax { get; set; }           // (5,4)
}
