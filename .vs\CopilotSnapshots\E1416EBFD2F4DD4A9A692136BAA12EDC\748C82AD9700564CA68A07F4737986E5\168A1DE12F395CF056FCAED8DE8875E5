﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Miller.WMS.Domain;

public class ElectrodeClassification : IEntity
{
    public Guid Id { get; set; }

    public Guid SpecificationId { get; set; }
    public Specification Specification { get; set; } = null!;

    public string Classification { get; set; } = null!;

    public decimal YieldStrength { get; set; }      // (6,3)
    public decimal TensileStrength { get; set; }    // (6,3)
    public decimal Elongation { get; set; }         // (5,4)

    public ElectrodeCoveringType? CoveringType { get; set; }

    public Guid? TungstenElectrodeClassificationId { get; set; }
    public TungstenElectrodeClassification? TungstenElectrodeClassification { get; set; }

    public Guid MetalChemicalCompositionId { get; set; }
    public MetalChemicalComposition MetalChemicalComposition { get; set; } = null!;
}
