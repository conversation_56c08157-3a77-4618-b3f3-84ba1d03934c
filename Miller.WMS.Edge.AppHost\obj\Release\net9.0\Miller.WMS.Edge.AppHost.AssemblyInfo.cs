//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute("0fb3be66-4f00-43d0-8571-b1e1d864aed3")]
[assembly: System.Reflection.AssemblyMetadata("dcpclipath", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.4.0\\tool" +
    "s\\dcp.exe")]
[assembly: System.Reflection.AssemblyMetadata("dcpextensionpaths", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.4.0\\tool" +
    "s\\ext\\")]
[assembly: System.Reflection.AssemblyMetadata("dcpbinpath", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.4.0\\tool" +
    "s\\ext\\bin\\")]
[assembly: System.Reflection.AssemblyMetadata("apphostprojectpath", "C:\\_\\Miller_Github\\Miller.WMS\\Miller.WMS.Edge.AppHost")]
[assembly: System.Reflection.AssemblyMetadata("apphostprojectname", "Miller.WMS.Edge.AppHost.csproj")]
[assembly: System.Reflection.AssemblyMetadata("aspiredashboardpath", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.dashboard.sdk.win-x64\\9.4.0\\tools\\Aspire" +
    ".Dashboard.exe")]
[assembly: System.Reflection.AssemblyMetadataAttribute("apphostprojectbaseintermediateoutputpath", "C:\\_\\Miller_Github\\Miller.WMS\\Miller.WMS.Edge.AppHost\\obj\\")]
[assembly: System.Reflection.AssemblyCompanyAttribute("Miller.WMS.Edge.AppHost")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Release")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0")]
[assembly: System.Reflection.AssemblyProductAttribute("Miller.WMS.Edge.AppHost")]
[assembly: System.Reflection.AssemblyTitleAttribute("Miller.WMS.Edge.AppHost")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]

// Generated by the MSBuild WriteCodeFragment class.

