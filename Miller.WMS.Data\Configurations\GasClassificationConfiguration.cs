﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class GasClassificationConfiguration : IEntityTypeConfiguration<GasClassification>
{
    public void Configure(EntityTypeBuilder<GasClassification> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Group)
               .HasMaxLength(5)
               .IsRequired();

        builder.Property(e => e.Subgroup)
               .HasMaxLength(5)
               .IsRequired();

        // Optional uniqueness if (Group, Subgroup) should be unique
        // builder.HasIndex(e => new { e.Group, e.Subgroup }).IsUnique();
    }
}
