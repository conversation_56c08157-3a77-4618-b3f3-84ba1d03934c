﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Miller.WMS.Domain;

public class CustomerFacility
{
    public int Id { get; set; }

    public int CustomerId { get; set; }
    public string Name { get; set; } = null!;
    public string? Address { get; set; }
    public string? AddressAdditionalInformation { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? ZipCode { get; set; }
    public string? Country { get; set; }

    public Customer Customer { get; set; } = null!;
}