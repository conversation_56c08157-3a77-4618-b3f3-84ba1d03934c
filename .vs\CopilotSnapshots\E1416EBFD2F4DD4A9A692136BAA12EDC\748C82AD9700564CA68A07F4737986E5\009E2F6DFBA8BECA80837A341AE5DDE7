﻿namespace Miller.WMS.Domain;


public enum OrganizationIndustryType
{
    Manufacturing = 0,
    ConstructionFabrication = 1
}

public enum OrganizationStatus
{
    Active = 0,
    Inactive = 1,
    Suspended = 2
}

public class Organization
{
    public int Id { get; set; }
    public required string Name { get; set; }

    public OrganizationIndustryType? IndustryType { get; set; }
    public OrganizationStatus Status { get; set; }
    public ICollection<Facility> Facilities { get; set; } = [];
    public ICollection<User> Users { get; set; } = [];
}