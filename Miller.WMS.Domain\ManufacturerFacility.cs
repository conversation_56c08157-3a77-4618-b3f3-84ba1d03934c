﻿using System;

namespace Miller.WMS.Domain;

public class ManufacturerFacility : IEntity
{
    public Guid Id { get; set; }

    public Guid ManufacturerId { get; set; }
    public Manufacturer Manufacturer { get; set; } = null!;

    public string Name { get; set; } = null!;
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? ZipCode { get; set; }
    public string? Country { get; set; }
}
