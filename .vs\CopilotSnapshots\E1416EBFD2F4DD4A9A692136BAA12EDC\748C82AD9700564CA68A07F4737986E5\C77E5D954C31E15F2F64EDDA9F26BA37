﻿using System;

namespace Miller.WMS.Domain;

public class ElectrodeMadeByManufacturer : IEntity
{
    public Guid ElectrodeId { get; set; }
    public Electrode Electrode { get; set; } = null!;

    public Guid ManufacturerId { get; set; }
    public Manufacturer Manufacturer { get; set; } = null!;

    public Guid? ManufacturerFacilityId { get; set; }
    public ManufacturerFacility? ManufacturerFacility { get; set; }
}
