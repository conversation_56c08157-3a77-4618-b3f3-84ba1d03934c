﻿namespace Miller.WMS.Domain;

public class ANumberElectrode : IEntity
{
    public Guid Id { get; set; }

    public required string Name { get; set; }

    public required Guid SpecificationId { get; set; }
    public Specification? Specification { get; set; }
    public required Guid MetalChemicalCompositionId { get; set; }
    public MetalChemicalComposition? MetalChemicalComposition { get; set; }
}
