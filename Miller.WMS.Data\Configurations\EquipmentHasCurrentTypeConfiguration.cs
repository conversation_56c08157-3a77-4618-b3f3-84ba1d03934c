﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class EquipmentHasCurrentTypeConfiguration : IEntityTypeConfiguration<EquipmentHasElectrodeClassification>
{
    public void Configure(EntityTypeBuilder<EquipmentHasElectrodeClassification> builder)
    {
        builder.HasKey(e => new { e.EquipmentId, e.ElectrodeClassificationId });

        builder.HasIndex(e => e.ElectrodeClassificationId);

        builder.HasOne(e => e.Equipment)
               .WithMany()
               .HasForeignKey(e => e.EquipmentId)
               .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.ElectrodeClassification)
               .WithMany()
               .HasForeignKey(e => e.ElectrodeClassificationId)
               .OnDelete(DeleteBehavior.Cascade);
    }
}
