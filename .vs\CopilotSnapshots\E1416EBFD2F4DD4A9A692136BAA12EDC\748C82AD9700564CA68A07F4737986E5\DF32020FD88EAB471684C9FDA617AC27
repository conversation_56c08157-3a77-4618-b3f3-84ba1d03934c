﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Miller.WMS.Domain;

public class CatElectrodeSpecialRule : IEntity
{
    public Guid Id { get; set; }

    public string WeldingArcDesignationSpec { get; set; } = null!;

    // FKs
    public Guid WeldingProcessId { get; set; }
    public Guid? SpecificationId { get; set; }

    // Other fields
    public decimal? MinimumYieldStrength { get; set; }

    // Navigation properties (assumed targets; adjust names/types if your model differs)
    public WeldingProcess WeldingProcess { get; set; }
    public Specification? Specification { get; set; }
}