﻿namespace Miller.WMS.Domain;

public class WorkCenter
{
    public int Id { get; set; }

    public required string Name { get; set; }
    public required string Description { get; set; }

    public WorkCenterType Type { get; set; }
    public WorkCenterStatus Status { get; set; }

    // Required
    public int FacilityId { get; set; }
    public required Facility Facility { get; set; }

    // Optional (hierarchical location)
    public int? FacilityAreaLevelOneId { get; set; }
    public FacilityAreaLevelOne? FacilityAreaLevelOne { get; set; }

    public int? FacilityAreaLevelTwoId { get; set; }
    public FacilityAreaLevelTwo? FacilityAreaLevelTwo { get; set; }

    public int? FacilityAreaLevelThreeId { get; set; }
    public FacilityAreaLevelThree? FacilityAreaLevelThree { get; set; }
}
