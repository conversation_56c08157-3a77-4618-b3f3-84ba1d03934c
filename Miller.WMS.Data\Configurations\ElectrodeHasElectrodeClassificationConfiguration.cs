﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class ElectrodeHasElectrodeClassificationConfiguration : IEntityTypeConfiguration<ElectrodeHasElectrodeClassification>
{
    public void Configure(EntityTypeBuilder<ElectrodeHasElectrodeClassification> builder)
    {
        builder.<PERSON><PERSON><PERSON>(e => new { e.ElectrodeId, e.ElectrodeClassificationId });

        builder.HasIndex(e => e.ElectrodeClassificationId);

        builder.HasOne(e => e.Electrode)
               .WithMany()
               .HasForeignKey(e => e.ElectrodeId)
               .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.ElectrodeClassification)
               .WithMany()
               .HasForeignKey(e => e.ElectrodeClassificationId)
               .OnDelete(DeleteBehavior.Cascade);
    }
}
