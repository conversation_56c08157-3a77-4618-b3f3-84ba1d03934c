﻿namespace Miller.WMS.Domain;

public class MaterialSubstitution
{
    public int Id { get; set; }

    public int SpecificationId { get; set; }
    public Specification Specification { get; set; } = null!;

    public int MaterialId { get; set; }
    public Material Material { get; set; } = null!;

    public int SubstitutedById { get; set; }
    public Material SubstitutedBy { get; set; } = null!;

    public MaterialSubstitutionApprovalType ApprovalType { get; set; }
}
