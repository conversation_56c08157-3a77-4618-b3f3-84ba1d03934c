﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class WorkCenterHasGasConfiguration : IEntityTypeConfiguration<WorkCenterHasGas>
{
    public void Configure(EntityTypeBuilder<WorkCenterHasGas> builder)
    {
        builder.HasKey(e => new { e.WorkCenterId, e.GasId });

        builder.HasIndex(e => e.GasId);

        builder.HasOne(e => e.WorkCenter)
               .WithMany()
               .HasForeignKey(e => e.WorkCenterId)
               .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Gas)
               .WithMany()
               .HasForeignKey(e => e.GasId)
               .OnDelete(DeleteBehavior.Cascade);
    }
}
