﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class FluxChemicalCompositionConfiguration : IEntityTypeConfiguration<FluxChemicalComposition>
{
    public void Configure(EntityTypeBuilder<FluxChemicalComposition> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Symbol)
               .HasMaxLength(20)
               .IsRequired();

        builder.Property(e => e.Description)
               .HasMaxLength(255)
               .IsRequired();

        builder.HasMany(e => e.Limits)
               .WithOne(e => e.FluxChemicalComposition)
               .HasForeignKey(e => e.FluxChemicalCompositionId)
               .OnDelete(DeleteBehavior.Cascade);
    }
}
