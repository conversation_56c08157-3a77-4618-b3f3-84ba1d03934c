﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class ElectrodeClassificationWeldingProcessConfiguration : IEntityTypeConfiguration<ElectrodeClassificationHasWeldingProcess>
{
    public void Configure(EntityTypeBuilder<ElectrodeClassificationHasWeldingProcess> builder)
    {
        builder.Has<PERSON><PERSON>(e => new { e.ElectrodeClassificationId, e.WeldingProcess });

        builder.HasIndex(e => e.WeldingProcess);

        builder.HasOne(e => e.ElectrodeClassification)
               .WithMany()
               .HasForeignKey(e => e.ElectrodeClassificationId)
               .OnDelete(DeleteBehavior.Cascade);

    }
}
