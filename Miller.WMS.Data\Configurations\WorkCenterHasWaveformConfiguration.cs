﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class WorkCenterHasWaveformConfiguration : IEntityTypeConfiguration<WorkCenterHasWaveform>
{
    public void Configure(EntityTypeBuilder<WorkCenterHasWaveform> builder)
    {
        builder.HasKey(e => new { e.WorkCenterId, e.WaveformId });

        builder.HasIndex(e => e.WaveformId);

        builder.HasOne(e => e.WorkCenter)
               .WithMany()
               .HasForeignKey(e => e.WorkCenterId)
               .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Waveform)
               .WithMany()
               .HasForeignKey(e => e.WaveformId)
               .OnDelete(DeleteBehavior.Cascade);
    }
}
