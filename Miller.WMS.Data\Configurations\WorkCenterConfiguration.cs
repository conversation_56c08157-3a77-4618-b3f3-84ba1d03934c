﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class WorkCenterConfiguration : IEntityTypeConfiguration<WorkCenter>
{
    public void Configure(EntityTypeBuilder<WorkCenter> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Name)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.Description)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.Type)
               .HasConversion<int>()
               .IsRequired();

        builder.Property(e => e.Status)
               .HasConversion<int>()
               .IsRequired();

        builder.HasOne(e => e.Facility)
               .WithMany()
               .HasForeignKey(e => e.FacilityId)
               .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.FacilityAreaLevelOne)
               .WithMany()
               .HasForeignKey(e => e.FacilityAreaLevelOneId)
               .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(e => e.FacilityAreaLevelTwo)
               .WithMany()
               .HasForeignKey(e => e.FacilityAreaLevelTwoId)
               .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(e => e.FacilityAreaLevelThree)
               .WithMany()
               .HasForeignKey(e => e.FacilityAreaLevelThreeId)
               .OnDelete(DeleteBehavior.SetNull);
    }
}
