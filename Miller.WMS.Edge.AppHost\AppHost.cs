

var builder = DistributedApplication.CreateBuilder(args);

var username = builder.AddParameter("username", "guest", secret: false);
var password = builder.AddParameter("password", "guest", secret: false);

var corePsql = builder.AddPostgres("wms-core-psql", username, password)
    .WithLifetime(ContainerLifetime.Persistent)
    .WithArgs("-c", "wal_level=logical") // required for CDC
    .WithDbGate();

var corePsqlDb = corePsql.AddDatabase("wms");

var coreSearch = builder.AddElasticsearch("wms-core-search")
    .WithLifetime(ContainerLifetime.Persistent)
    ;

var dataService = builder.AddProject<Projects.Miller_WMS_Core_DataService>("wms-core-dataservice")
    .WithReference(corePsqlDb)
    .WaitFor(corePsqlDb);

// Add Java-based CDC service using Debezium Engine
var cdcService = builder.AddSpringApp("wms-core-cdc", "../Miller.WMS.Core.CDC",
    new JavaAppExecutableResourceOptions
    {
        ApplicationName = "target/miller-wms-core-cdc-1.0.0.jar",
        OtelAgentPath = "../../../agents"
    })
    .WithHttpHealthCheck("/ping")
    .WithEnvironment("DB_HOST", corePsql.GetEndpoint("tcp").Property(EndpointProperty.Host))
    .WithEnvironment("DB_PORT", corePsql.GetEndpoint("tcp").Property(EndpointProperty.Port))
    .WithEnvironment("DB_NAME", "wms")
    .WithEnvironment("DB_USER", username)
    .WithEnvironment("DB_PASSWORD", password)
    .WithEnvironment("ES_HOST", coreSearch.GetEndpoint("http").Property(EndpointProperty.Host))
    .WithEnvironment("ES_PORT", coreSearch.GetEndpoint("http").Property(EndpointProperty.Port))
    .WithEnvironment("ES_INDEX_NAME", "organizations")
    .WithEnvironment("HEALTH_PORT", "8080")
    .WithReference(corePsqlDb)
    .WithReference(coreSearch)
    .WaitFor(corePsqlDb)
    .WaitFor(coreSearch)
    .WaitFor(dataService); // Wait for data service to seed the database first

var cache = builder.AddRedis("wms-edge-cache");

var apiService = builder.AddProject<Projects.Miller_WMS_Edge_ApiService>("wms-edge-api")
    .WithHttpHealthCheck("/health");

builder.AddProject<Projects.Miller_WMS_Edge_Web>("wms-edge-web")
    .WithExternalHttpEndpoints()
    .WithHttpHealthCheck("/health")
    .WithReference(cache)
    .WaitFor(cache)
    .WithReference(apiService)
    .WaitFor(apiService);

builder.Build().Run();
