﻿namespace Miller.WMS.Domain;

public enum EquipmentType
{
    Welding = 0,
    Machining = 1,
    Assembly = 2,
    Inspection = 3,
    Safety = 4,
    Induction = 5,
    Cleaning = 6,
    Other = 7
}

public enum EquipmentSubType
{
    PowerSupply = 0,
    Torch = 1,
    Feeder = 2,
    ContactTip = 3,
    PreHeat = 4,
    PostHeat = 5,
    Cleaning = 6,
    ToeTreatment = 7,
    FumeExtraction = 8,
    GrindingSanding = 9,
    Filtering = 10
}

public enum EquipmentStatus
{
    // TODO: confirm actual 3 statuses
    Active = 0,
    Inactive = 1,
    Retired = 2
}

public class Equipment
{
    public int Id { get; set; }

    public int ManufacturerId { get; set; }
    public Manufacturer Manufacturer { get; set; } = null!;

    public EquipmentType Type { get; set; }
    public EquipmentSubType SubType { get; set; }
    public string Model { get; set; } = null!;
    public EquipmentStatus Status { get; set; }
}
